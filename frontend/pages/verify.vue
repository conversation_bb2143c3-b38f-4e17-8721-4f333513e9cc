<template>
	<NuxtLayout name="auth">
		<div class="space-y-6 sm:space-y-10 -mt-4 md:mt-0">
			<!-- Verify Mode -->
			<template v-if="mode === 'verified'">
				<div class="text-center space-y-4">
					<UIcon
						name="i-lucide-circle-check-big"
						class="size-12 text-(--ui-success)" />
					<h1 class="text-xl md:text-2xl font-medium">Email Verified</h1>
					<p>Your email was successfully verified. Please click below to continue.</p>
				</div>
				<UButton
					block
					size="xl"
					class="text-sm py-3"
					to="/">
					Continue
				</UButton>
			</template>

			<!-- Resend Mode -->
			<template v-else-if="mode === 'resend' && !hasError">
				<div class="text-center space-y-4">
					<UIcon
						:name="sent ? 'i-lucide-circle-check-big' : 'i-lucide-mail-check'"
						:class="sent ? 'size-12 text-(--ui-success)' : 'size-12 text-(--ui-neutral)'" />
					<h1 class="text-xl md:text-2xl font-medium">
						{{ sent ? 'Verification Sent' : 'Please Verify Your Account' }}
					</h1>
					<p>
						{{
							sent
								? 'We have sent the verification email. Check your inbox.'
								: 'Please click the link we sent to you when you signed up or click below to resend.'
						}}
					</p>
				</div>

				<!-- Error Alert -->
				<UAlert
					v-if="resendError"
					color="error"
					variant="soft"
					:title="resendError"
					:close-button="{ icon: 'i-lucide-x', color: 'gray', variant: 'link', padded: false }"
					@close="resendError = null" />

				<UButton
					v-if="!sent"
					block
					size="xl"
					class="text-sm py-3"
					@click="handleResend"
					:loading="loading">
					Resend Verification Email
				</UButton>
				<UButton
					v-else
					block
					size="xl"
					class="text-sm py-3"
					color="neutral"
					variant="soft"
					disabled>
					Verification Sent
				</UButton>
			</template>

			<!-- Error Logged In -->
			<template v-else-if="mode === 'resend' && hasError">
				<div class="text-center space-y-4">
					<UIcon
						name="i-lucide-frown"
						class="size-12 text-(--ui-error)" />
					<h1 class="text-xl md:text-2xl font-medium">There Was A Problem</h1>
					<p>The verification link is invalid or expired. Please click below to resend the verification email.</p>
				</div>
				<UButton
					v-if="!sent"
					block
					size="xl"
					class="text-sm py-3"
					@click="handleResend"
					:loading="loading">
					Resend Verification Email
				</UButton>
				<UButton
					v-else
					block
					size="xl"
					class="text-sm py-3"
					color="neutral"
					variant="soft"
					disabled>
					Verification Sent
				</UButton>
			</template>

			<!-- Error Logged Out -->
			<template v-else-if="mode === 'logIn' && hasError">
				<div class="text-center space-y-4">
					<UIcon
						name="i-lucide-frown"
						class="size-12 text-(--ui-error)" />
					<h1 class="text-xl md:text-2xl font-medium">There Was A Problem</h1>
					<p>The verification link is invalid or expired. Please log in to resend the verification email.</p>
				</div>
			</template>

			<!-- Login -->
			<template v-else-if="mode === 'logIn' && !hasError">
				<div class="text-center space-y-4">
					<UIcon
						name="i-lucide-log-in"
						class="size-12 text-(--ui-muted)" />
					<h1 class="text-xl md:text-2xl font-medium">Please Log In</h1>
					<p>Log in to access your account and verify your email.</p>
				</div>
				<UButton
					block
					size="xl"
					class="text-sm py-3"
					to="/sign-in">
					Log In
				</UButton>
			</template>
		</div>
	</NuxtLayout>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from 'vue';
import { authClient, useAuthSession } from '~/server/utils/auth-client';

definePageMeta({
	title: 'Verify Account',
	layout: false,
});

// Types
type Mode = 'verified' | 'resend' | 'logIn';

// Session and route
const { data: session } = await useAuthSession(useFetch);
const route = useRoute();
const loading = ref<boolean>(false);
const sent = ref<boolean>(false); // Track whether the email has been sent

// Computed error state with proper typing
const hasError = computed(() => {
	const errorParam = route.query.error;
	return typeof errorParam === 'string' && ['invalid_token', 'expired_token', 'already_verified'].includes(errorParam);
});

// Writable mode
const mode = ref<Mode>('logIn');

// Initialize mode
const initializeMode = () => {
	if (session.value) {
		mode.value = session.value.user.emailVerified ? 'verified' : 'resend';
	} else {
		mode.value = 'logIn';
	}
};
initializeMode();

// Watch for changes in route or session state
watch(() => [route.query.error, session.value?.user.emailVerified], initializeMode);

// Error state for user feedback
const resendError = ref<string | null>(null);

// Handle resend email action
const handleResend = async () => {
	if (!session.value?.user.email) return;

	loading.value = true;
	resendError.value = null; // Clear previous errors

	try {
		await authClient.sendVerificationEmail({
			email: session.value.user.email,
			callbackURL: '/verify', // The redirect URL after verification
		});
		sent.value = true; // Mark as sent
	} catch (error) {
		console.error('Failed to resend verification email:', error);

		// Provide user-friendly error messages
		if (error instanceof Error) {
			resendError.value = error.message.includes('rate limit')
				? 'Please wait before requesting another verification email.'
				: 'Failed to send verification email. Please try again.';
		} else {
			resendError.value = 'An unexpected error occurred. Please try again.';
		}
	} finally {
		loading.value = false; // Reset loading state
	}
};
</script>
